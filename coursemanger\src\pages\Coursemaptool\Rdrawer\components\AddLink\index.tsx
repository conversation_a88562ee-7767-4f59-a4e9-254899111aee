import React, { FC, useEffect, useState } from 'react';
import './index.less';
import useLocale from '@/hooks/useLocale';
import { Button, List, Select, Modal, Form, Input } from 'antd';
import { name } from 'pubsub-js';

interface IProps {
  selectNode: any;
  updatanode: any;
  x6node: any;
  isedit: boolean;
  setSelectNode: any;
}

const AddLink:FC<IProps> = ({ selectNode, updatanode, x6node, isedit,setSelectNode }) => {
    const { t } = useLocale();
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [form] = Form.useForm();

    // 显示弹窗
    const showModal = () => {
        setIsModalVisible(true);
    };

    // 处理弹窗确认
    const handleOk = () => {
        form.validateFields().then(values => {
            const { displayText, linkUrl } = values;
            const newLink = {
                name: displayText,
                link: linkUrl,
            }; // 使用 | 分隔显示文字和链接

            const currentLinks = selectNode?.bindlink || [];
            const updatedLinks = [...currentLinks, newLink];

            setSelectNode({
                ...selectNode,
                bindlink: updatedLinks,
                temp: new Date().getTime(),
            });

            updatanode(x6node.id, {
                bindlink: updatedLinks,
            });

            form.resetFields();
            setIsModalVisible(false);
        }).catch(info => {
            console.log('Validate Failed:', info);
        });
    };

    // 处理弹窗取消
    const handleCancel = () => {
        form.resetFields();
        setIsModalVisible(false);
    };

    return (
        <>
            <div className="video_view">
                <div className="redio_view"></div>
                <span className="title_span">{t('外部链接')}</span>
                <div style={{ flex: 1, textAlign: 'right' }}>
                    {isedit && (
                    <Button size="small" type="primary" onClick={showModal}>{t('添加')}</Button>
                    )}
                </div>
                </div>

            <List
                bordered
                className="link_list"
                locale={{ emptyText: t('暂无链接地址') }}
                dataSource={selectNode?.bindlink || []}
                renderItem={(item: any, index: number) => {
                    // 解析显示文字和链接地址
                    const parts = item

                    return (
                        <List.Item style={{ cursor: 'pointer' }} key={index}>
                            <a
                                href={linkUrl}
                                target="_blank"
                                className="link_overflow"
                                title={item.name}
                            >
                                {item.name}
                            </a>
                        </List.Item>
                    );
                }}
            />

            {/* 添加链接弹窗 */}
            <Modal
                title={t('添加外部链接')}
                open={isModalVisible}
                onOk={handleOk}
                onCancel={handleCancel}
                okText={t('确定')}
                cancelText={t('取消')}
            >
                <Form
                    form={form}
                    layout="vertical"
                    name="addLinkForm"
                >
                    <Form.Item
                        label={t('显示文字')}
                        name="displayText"
                        rules={[
                            { required: true, message: t('请输入显示文字') }
                        ]}
                    >
                        <Input placeholder={t('请输入链接的显示文字')} />
                    </Form.Item>

                    <Form.Item
                        label={t('链接地址')}
                        name="linkUrl"
                        rules={[
                            { required: true, message: t('请输入链接地址') },
                            { type: 'url', message: t('请输入有效的链接地址') }
                        ]}
                    >
                        <Input placeholder={t('请输入完整的链接地址，如：https://example.com')} />
                    </Form.Item>
                </Form>
            </Modal>
        </>
    )
};

export default AddLink;