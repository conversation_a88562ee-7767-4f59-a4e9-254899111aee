import React, { FC, useEffect, useState } from 'react';
import './index.less';
import useLocale from '@/hooks/useLocale';
import { Button, List, Select } from 'antd';

interface IProps {
  selectNode: any;
  updatanode: any;
  x6node: any;
  isedit: boolean;
  setSelectNode: any;
}

const AddLink:FC<IProps> = ({ selectNode, updatanode, x6node, isedit,setSelectNode }) => { 
    const { t } = useLocale();

    return (
        <>
            <div className="video_view">
                <div className="redio_view"></div>
                <span className="title_span">{t('外部链接')}</span>
                <div style={{ flex: 1, textAlign: 'right' }}>
                    {isedit && (
                    <Button size="small" type="primary">{t('添加')}</Button>
                    )}
                </div>
                </div>

                {isedit ? (
                <Select
                    mode="tags"
                    style={{ width: '100%' }}
                    placeholder={t('请输入链接地址，如果有多个请用回车隔开')}
                    onChange={e => {
                    setSelectNode({
                        ...selectNode,
                        bindlink: e,
                        temp: new Date().getTime(),
                    });
                    updatanode(x6node.id, {
                        // ...selectNode,
                        bindlink: e,
                    });
                    }}
                    value={selectNode?.bindlink || []}
                    options={[]}
                />
                ) : (
                <List
                    bordered
                    className="link_list"
                    locale={{ emptyText: t('暂无链接地址') }}
                    dataSource={selectNode?.bindlink || []}
                    renderItem={(item: any, index: number) => (
                    <List.Item style={{ cursor: 'pointer' }} key={index}>
                        <a
                        href={item}
                        target="_blank"
                        className="link_overflow"
                        title={item}
                        >
                        {item}
                        </a>
                    </List.Item>
                    )}
                />
            )}
        </>
    )
};

export default AddLink;